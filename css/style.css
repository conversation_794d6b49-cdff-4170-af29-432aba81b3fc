:root {
    /* Dark theme colors */
    --bg-primary: #0a192f;
    --bg-secondary: #112240;
    --text-primary: #e6f1ff;
    --text-secondary: #8892b0;
    --accent: #64ffda;
    --nav-bg: rgba(10, 25, 47, 0.85);
    
    /* Light theme colors */
    --light-bg-primary: #f5f5f5;
    --light-bg-secondary: #ffffff;
    --light-text-primary: #2d3748;
    --light-text-secondary: #4a5568;
    --light-accent: #0070f3;
    --light-nav-bg: #8892b0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
@font-face {
    font-family: 'Bronx';
    src: url('../fonts/Bronx.ttf');
    font-weight: 400;
    font-style: normal;
    font-display: swap;
}
body {
    font-family: 'Inter', sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    transition: background-color 0.3s ease;
}

body.light-mode {
    --bg-primary: var(--light-bg-primary);
    --bg-secondary: var(--light-bg-secondary);
    --text-primary: var(--light-text-primary);
    --text-secondary: var(--light-text-secondary);
    --accent: var(--light-accent);
    --nav-bg: var(--light-nav-bg);
}

.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: var(--nav-bg);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
}

.nav-content {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
}

.nav-links a {
    color: var(--text-primary);
    text-decoration: none;
    margin-left: 2rem;
    transition: color 0.3s ease;
    position: relative;
}

.nav-links a::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: -4px;
    left: 0;
    background-color: var(--accent);
    transition: width 0.3s ease;
}

.nav-links a:hover::after,
.nav-links a.active::after {
    width: 100%;
}

#theme-toggle {
    background: none;
    border: none;
    cursor: pointer;
    height: 40px;
    width: 40px;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--accent);
}

section {
    min-height: 100vh;
    padding: 100px 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.hero {
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 30%, rgba(100, 255, 218, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(100, 255, 218, 0.03) 0%, transparent 50%);
    z-index: 0;
}

.code-symbols {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    opacity: 0.1;
}

.code-symbol {
    position: absolute;
    font-family: 'Fira Code', monospace;
    color: var(--accent);
    user-select: none;
    animation: float 6s ease-in-out infinite;
    
}

.code-symbol:nth-child(1) { top: 10%; left: 10%; animation-delay: 0s; }
.code-symbol:nth-child(2) { top: 20%; right: 15%; animation-delay: 1s; }
.code-symbol:nth-child(3) { bottom: 30%; left: 20%; animation-delay: 2s; }
.code-symbol:nth-child(4) { bottom: 20%; right: 25%; animation-delay: 3s; }
.code-symbol:nth-child(5) { top: 50%; left: 30%; animation-delay: 4s; }
.code-symbol:nth-child(6) { top: 30%; right: 30%; animation-delay: 5s; }

.hero-content {
    position: relative;
    z-index: 2;
}

.hero h1 {
    font-size: 5.5rem;
    margin-bottom: 1rem;
    font-family: 'Bronx', 'Inter', sans-serif;
    font-weight: 500;
    letter-spacing: 10px;
}

.hero p{
    color: var(--accent);
    font-size: 2.2rem;
}

#typing-text {
    font-size: 2.2rem;
    color: var(--accent);
    line-height: 1.2;
}
.project-card {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    transition: transform 0.3s ease;
}

.project-card:hover {
    transform: translateY(-5px);
}

.tech-tags span {
    background: var(--bg-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.875rem;
    margin-right: 0.5rem;
}

/* Add more styles as needed */

/* Add Experience Section Styles */
.timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem 0;
}

.experience-card {
    background: transparent;
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    backdrop-filter: blur(2.8px);
}

.experience-card:hover {
    background: var(--bg-secondary);
    transform: translateY(-5px);
    border: 1px solid var(--accent);
}

.experience-card .date {
    color: var(--accent);
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.experience-card h3 {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 0.25rem;
}

.experience-card h4 {
    font-size: 1.1rem;
    color: var(--text-secondary);
    margin-bottom: 1rem;
}

.experience-card p {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.tech-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tech-tags span {
    color: var(--accent);
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    transition: background-color 0.3s ease;
}

.experience-card:hover .tech-tags span {
    background: rgba(100, 255, 218, 0.1);
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .profile-image {
        width: 250px;
        height: 250px;
        margin: 0 auto;
    }

    .experience-card {
        padding: 1.5rem;
    }

    .experience-card h3 {
        font-size: 1.25rem;
    }

    .experience-card h4 {
        font-size: 1rem;
    }
}

/* About Section Styles */
.about {
    display: flex;
    align-items: center;
    padding: 100px 2rem;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
    max-width: 1000px;
    margin: 0 auto;
}

.about-text {
    position: relative;
}

.about-text h2 {
    font-size: 2.5rem;
    margin-bottom: 2rem;
    color: var(--text-primary);
    position: relative;
    display: inline-block;
}

.about-text h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 60%;
    height: 2px;
    background-color: var(--accent);
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    position: relative;
    padding-left: 1.5rem;
    border-left: 2px solid rgba(100, 255, 218, 0.1);
}

.about-text p:hover {
    border-left-color: var(--accent);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.about-text .highlight {
    color: var(--accent);
    font-weight: 500;
}

.tech-list {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(100, 255, 218, 0.1);
}

.tech-item {
    display: flex;
    align-items: center;
    color: var(--text-secondary);
    font-family: 'SF Mono', 'Fira Code', monospace;
    font-size: 0.9rem;
}

.tech-item::before {
    content: '▹';
    color: var(--accent);
    margin-right: 1rem;
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: left;
    }

    .about-text p {
        font-size: 1rem;
        line-height: 1.7;
    }

    .tech-list {
        grid-template-columns: 1fr;
    }
}

.profile-image {
    position: relative;
    width: 300px;
    height: 300px;
}

.profile-image img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: cover;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

.profile-image:hover img {
    filter: grayscale(0%);
}

.profile-image::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid var(--accent);
    border-radius: 8px;
    z-index: -1;
    transition: all 0.3s ease;
}

.profile-image:hover::after {
    top: 10px;
    left: 10px;
    right: -10px;
    bottom: -10px;
}

/* Project Section Styles */
.projects {
    padding: 100px 2rem;
}

.project-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 2rem;
    margin-top: 3rem;
}

.project-card {
    background: transparent;
    border-radius: 8px;
    padding: 2rem;
    height: 100%;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    backdrop-filter: blur(2.8px);
    border: 1px solid transparent;
}


.project-card::before {
    content: '';
    position: absolute;
    inset: 0;
    border-radius: 8px;
    background: var(--bg-secondary);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.project-card:hover::before {
    opacity: 1;
}

.project-card:hover {
    background: var(--bg-secondary);
    transform: translateY(-5px);
    border: 1px solid var(--accent);
}


.project-content {
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.project-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.folder-icon svg {
    width: 40px;
    height: 40px;
    color: var(--accent);
}

.project-links {
    display: flex;
    gap: 1rem;
}

.project-links a svg {
    width: 20px;
    height: 20px;
    color: var(--text-secondary);
    transition: color 0.3s ease;
}

.project-links a:hover svg {
    color: var(--accent);
}

.project-card h3 {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
}

.project-card p {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.6;
    margin-bottom: 1.5rem;
    flex-grow: 1;
}

.tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    font-family: 'SF Mono', 'Fira Code', monospace;
    font-size: 0.8rem;
    color: var(--text-secondary);
}

.tech-stack span {
    transition: color 0.3s ease;
}

.project-card:hover .tech-stack span {
    color: var(--accent);
}

@media (max-width: 768px) {
    .project-grid {
        grid-template-columns: 1fr;
    }
}

/* Contact Section Styles */
.contact {
    padding: 100px 2rem;
    position: relative;
    overflow: hidden;
}

.contact-container {
    max-width: 1000px;
    margin: 0 auto;
    position: relative;
}

.contact-header {
    text-align: center;
    margin-bottom: 4rem;
    position: relative;
}

.header-line {
    width: 100px;
    height: 4px;
    background: var(--accent);
    margin: 1.5rem auto;
    position: relative;
    border-radius: 2px;
}

.header-line::before,
.header-line::after {
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background: var(--accent);
    border-radius: 50%;
    top: -2px;
}

.header-line::before {
    left: -4px;
}

.header-line::after {
    right: -4px;
}

.contact-subtitle {
    color: var(--text-secondary);
    font-size: 1.1rem;
    margin-top: 1rem;
}

.contact-content {
    position: relative;
    z-index: 1;
    display: grid;
    grid-template-columns: 1.5fr 1fr;
    gap: 3rem;
}

.contact-form-container {
    background: var(--bg-secondary);
    border-radius: 20px;
    padding: 3rem;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(100, 255, 218, 0.1);
    transition: transform 0.3s ease, border-color 0.3s ease;
}

.contact-form-container:hover {
    transform: translateY(-5px);
    border-color: var(--accent);
}

.form-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.decoration-circle {
    position: absolute;
    width: 200px;
    height: 200px;
    border: 2px solid var(--accent);
    border-radius: 50%;
    top: -100px;
    left: -100px;
    opacity: 0.1;
}

.decoration-line {
    position: absolute;
    width: 150%;
    height: 150%;
    border: 2px solid var(--accent);
    border-radius: 63% 37% 54% 46% / 55% 48% 52% 45%;
    top: -25%;
    left: -25%;
    animation: rotate 20s linear infinite;
    opacity: 0.1;
}

.input-animation-wrapper {
    position: relative;
    margin-bottom: 2rem;
}

.input-animation-wrapper input,
.input-animation-wrapper textarea {
    width: 100%;
    padding: 1rem;
    background: transparent;
    border: none;
    color: var(--text-primary);
    font-size: 1rem;
    position: relative;
    z-index: 1;
}

.input-animation-wrapper label {
    position: absolute;
    left: 1rem;
    top: 1rem;
    color: var(--text-secondary);
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 0;
}

.input-underline {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: var(--text-secondary);
    transition: all 0.3s ease;
}

.input-animation-wrapper input:focus ~ .input-underline,
.input-animation-wrapper textarea:focus ~ .input-underline,
.input-animation-wrapper input:not(:placeholder-shown) ~ .input-underline,
.input-animation-wrapper textarea:not(:placeholder-shown) ~ .input-underline {
    background: var(--accent);
    height: 2px;
}

.input-animation-wrapper input:focus ~ label,
.input-animation-wrapper textarea:focus ~ label,
.input-animation-wrapper input:not(:placeholder-shown) ~ label,
.input-animation-wrapper textarea:not(:placeholder-shown) ~ label {
    transform: translateY(-2rem) scale(0.8);
    color: var(--accent);
}

.submit-btn {
    width: 100%;
    padding: 1rem;
    background: transparent;
    border: 2px solid var(--accent);
    border-radius: 8px;
    color: var(--accent);
    font-size: 1rem;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    z-index: 1;
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: var(--accent);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.submit-btn:hover::before {
    width: 300px;
    height: 300px;
}

.submit-btn:hover {
    color: var(--bg-primary);
}

.btn-icon {
    display: inline-flex;
    transition: transform 0.3s ease;
}

.submit-btn:hover .btn-icon {
    transform: translateX(5px) rotate(-45deg);
}

.social-links-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    color: var(--text-secondary);
    text-decoration: none;
    transition: all 0.3s ease;
    background: rgba(100, 255, 218, 0.05);
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(100, 255, 218, 0.1);
    transition: all 0.3s ease;
}

.social-icon svg {
    width: 20px;
    height: 20px;
    transition: transform 0.3s ease;
}

.social-text {
    font-size: 1rem;
    font-weight: 500;
}

.social-link:hover {
    background: rgba(100, 255, 218, 0.1);
    color: var(--accent);
    transform: translateX(10px);
}

.social-link:hover .social-icon {
    background: var(--accent);
    color: var(--bg-primary);
}

.social-link:hover .social-icon svg {
    transform: scale(1.1);
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form-container {
        padding: 2rem;
    }

    .social-links {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .social-link {
        flex: 1 1 auto;
        min-width: 150px;
    }
}

/* Add these responsive styles */

/* General responsive container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

/* Navigation responsive styles */
@media (max-width: 768px) {
    .navbar {
        padding: 1rem 0;
    }

    .nav-content {
        padding: 0 1rem;
    }

    .nav-links {
        position: fixed;
        top: 0;
        right: -100%;
        height: 100vh;
        width: 75%;
        background: var(--bg-secondary);
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        transition: right 0.3s ease;
        padding: 2rem;
    }

    .nav-links.active {
        right: 0;
    }

    .hamburger {
        display: block;
        z-index: 1000;
    }

    .nav-links a {
        margin: 0;
        font-size: 1.2rem;
    }
}

/* Hero section responsive */
@media (max-width: 768px) {
    .hero {
        padding: 120px 1rem;
        text-align: center;
    }

    .hero h1 {
        font-size: 2.5rem;
    }

    .hero .subtitle {
        font-size: 1.1rem;
    }
}

/* About section responsive */
@media (max-width: 992px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 3rem;
        text-align: center;
    }

    .profile-image {
        margin: 0 auto;
    }
}

/* Experience section responsive */
@media (max-width: 768px) {
    .experience-card {
        padding: 1.5rem;
    }

    .timeline::before {
        left: 15px;
    }

    .tech-tags {
        justify-content: center;
    }
}

/* Projects section responsive */
@media (max-width: 992px) {
    .project-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    .project-grid {
        grid-template-columns: 1fr;
    }

    .project-card {
        padding: 1.5rem;
    }
}

/* Contact section responsive */
@media (max-width: 768px) {
    .contact-container {
        padding: 0 1rem;
    }

    .contact-form-container {
        padding: 1.5rem;
    }

    .social-links {
        gap: 1.5rem;
    }
}

/* General responsive text adjustments */
@media (max-width: 768px) {
    h1 {
        font-size: 2.5rem;
    }

    h2 {
        font-size: 2rem;
    }

    h3 {
        font-size: 1.5rem;
    }

    p {
        font-size: 0.95rem;
    }
}

/* Add these styles for the hamburger menu */
.hamburger {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    position: relative;
    z-index: 1000;
}

.hamburger span {
    display: block;
    width: 25px;
    height: 2px;
    background-color: var(--text-primary);
    margin: 5px 0;
    transition: all 0.3s ease;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Add this animation for the floating effect */
@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

/* Add a subtle gradient animation */
@keyframes gradientShift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

.hero::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        45deg,
        transparent 0%,
        rgba(100, 255, 218, 0.02) 50%,
        transparent 100%
    );
    background-size: 200% 200%;
    animation: gradientShift 15s ease infinite;
    z-index: 1;
}

/* Hamburger Menu Styles */
.hamburger {
    display: none;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1000;
}

.hamburger span {
    display: block;
    width: 25px;
    height: 2px;
    background-color: var(--text-primary);
    margin: 5px 0;
    transition: all 0.3s ease;
}

/* Mobile Navigation Styles */
@media (max-width: 768px) {
    .hamburger {
        display: block;
        position: relative;
    }

    .nav-links {
        position: fixed;
        top: 0;
        right: -100%;
        height: 100vh;
        width: 75%;
        background: var(--bg-secondary);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        transition: right 0.3s ease;
        padding: 2rem;
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
    }

    .nav-links.active {
        right: 0;
    }

    .nav-links a {
        margin: 0;
        font-size: 1.2rem;
        width: 100%;
        text-align: center;
        padding: 1rem;
    }

    /* Fix Safari compatibility issues */
    .code-symbol {
        font-family: 'Fira Code', monospace;
        color: var(--accent);
        -webkit-user-select: none;
        user-select: none;
        animation: float 6s ease-in-out infinite;
    }

    .experience-card {
        -webkit-backdrop-filter: blur(2.8px);
        backdrop-filter: blur(2.8px);
    }

    .project-card {
        -webkit-backdrop-filter: blur(2.8px);
        backdrop-filter: blur(2.8px);
    }

    /* Theme toggle button positioning for mobile */
    #theme-toggle {
        margin-top: 2rem;
    }

    /* Prevent scrolling when menu is open */
    body.menu-open {
        overflow: hidden;
    }

    /* Ensure hamburger stays on top */
    .hamburger {
        z-index: 1001;
    }

    /* Add background to navbar for better visibility */
    .navbar {
        background: var(--bg-primary);
        -webkit-backdrop-filter: blur(10px);
        backdrop-filter: blur(10px);
    }
}

/* Also fix the Safari compatibility issues */
.code-symbol {
    font-family: 'Fira Code', monospace;
    color: var(--accent);
    -webkit-user-select: none;
    user-select: none;
    animation: float 6s ease-in-out infinite;
}

.experience-card {
    /* ... existing styles ... */
    -webkit-backdrop-filter: blur(2.8px);
    backdrop-filter: blur(2.8px);
}

.project-card {
    /* ... existing styles ... */
    -webkit-backdrop-filter: blur(2.8px);
    backdrop-filter: blur(2.8px);
}

/* Update profile image styles */
.profile-image {
    position: relative;
    width: 300px;
    height: 300px;
}

.profile-image img {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: cover;
    filter: grayscale(100%);
    transition: filter 0.3s ease;
}

/* Handle hover on non-touch devices */
@media (hover: hover) {
    .profile-image:hover img {
        filter: grayscale(0%);
    }

    .profile-image:hover::after {
        top: 10px;
        left: 10px;
        right: -10px;
        bottom: -10px;
    }
}

/* Handle touch devices */
.touch-device .profile-image.touch-active img {
    filter: grayscale(0%);
}

.touch-device .profile-image.touch-active::after {
    top: 10px;
    left: 10px;
    right: -10px;
    bottom: -10px;
}

.profile-image::after {
    content: '';
    position: absolute;
    top: 15px;
    left: 15px;
    right: -15px;
    bottom: -15px;
    border: 2px solid var(--accent);
    border-radius: 8px;
    z-index: -1;
    transition: all 0.3s ease;
}

@media (max-width: 768px) {
    .profile-image {
        width: 250px;
        height: 250px;
        margin: 0 auto;
    }
}