@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(100px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-10deg) scale(0.8);
    }
    to {
        opacity: 1;
        transform: rotate(0) scale(1);
    }
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

@keyframes staggerFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-hidden {
    opacity: 0;
    transform: translateY(50px);
    transition: all 1s ease;
}

.section-visible {
    opacity: 1 !important;
    transform: translateY(0) !important;
}

.hero-content {
    animation: fadeIn 1s ease-out;
}

.hero h1 {
    animation: slideInLeft 1s ease-out;
}

.typewriter {
    animation: slideInRight 1s ease-out 0.3s backwards;
}

.subtitle {
    animation: slideInUp 1s ease-out 0.6s backwards;
}

.about-content.section-visible .profile-image {
    animation: rotateIn 1s ease-out;
}

.about-content.section-visible .about-text {
    animation: slideInRight 1s ease-out 0.3s backwards;
}

.experience-card {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.experience-card.visible {
    opacity: 1;
    transform: translateY(0);
}

.experience-card:nth-child(odd).visible {
    animation: slideInLeft 0.7s ease-out forwards;
}

.experience-card:nth-child(even).visible {
    animation: slideInRight 0.7s ease-out forwards;
}

.tech-tags span {
    opacity: 0;
    animation: staggerFadeIn 0.5s ease-out forwards;
}

.experience-card.visible .tech-tags span:nth-child(1) { animation-delay: 0.1s; }
.experience-card.visible .tech-tags span:nth-child(2) { animation-delay: 0.2s; }
.experience-card.visible .tech-tags span:nth-child(3) { animation-delay: 0.3s; }
.experience-card.visible .tech-tags span:nth-child(4) { animation-delay: 0.4s; }

.project-card {
    opacity: 1;
    transform: translateY(0);
    transition: all 0.3s ease;
}

.project-card.visible {
    opacity: 1;
    transform: translateY(0);
    animation: scaleIn 0.7s ease-out forwards;
}

.project-card:hover {
    animation: float 3s ease-in-out infinite;
    transform: translateY(-5px);
}

.contact-form-container.section-visible {
    animation: slideInLeft 1s ease-out;
}

.social-links-container.section-visible {
    animation: slideInRight 1s ease-out 0.3s backwards;
}

.social-link {
    opacity: 0;
    animation: staggerFadeIn 0.5s ease-out forwards;
}

.contact-content.section-visible .social-link:nth-child(1) { animation-delay: 0.4s; }
.contact-content.section-visible .social-link:nth-child(2) { animation-delay: 0.6s; }
.contact-content.section-visible .social-link:nth-child(3) { animation-delay: 0.8s; }

.torch-effect {
    position: fixed;
    pointer-events: none;
    width: 600px;
    height: 600px;
    background: radial-gradient(circle, rgba(100,255,218,0.1) 0%, rgba(255,255,255,0) 70%);
    transform: translate(-50%, -50%);
    z-index: 9999;
    transition: all 0.1s ease;
}

@media (prefers-reduced-motion: reduce) {
    * {
        animation: none !important;
        transition: none !important;
    }
} 